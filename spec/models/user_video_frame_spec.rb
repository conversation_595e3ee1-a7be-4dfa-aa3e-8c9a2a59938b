require 'rails_helper'

RSpec.describe UserVideoFrame, type: :model do
  let(:user) { FactoryBot.create(:user) }
  let(:font) do
    Font.find_by(name_font: "Noto Sans Telugu", badge_font: "Noto Sans Telugu") ||
      FactoryBot.create(:font, name_font: "Noto Sans Telugu", badge_font: "Noto Sans Telugu")
  end
  let(:video_frame) { FactoryBot.create(:video_frame, font: font) }
  let(:user_video_frame) { FactoryBot.create(:user_video_frame, user: user, video_frame: video_frame) }

  describe 'associations' do
    it { should belong_to(:user) }
    it { should belong_to(:video_frame) }
    it { should have_many(:user_video_posters).dependent(:destroy) }
  end

  describe 'validations' do
    it { should validate_presence_of(:user_id) }
    it { should validate_presence_of(:video_frame_id) }
    it { should validate_presence_of(:identity_photo_url) }
  end

  describe 'scopes' do
    describe '.active' do
      let!(:active_frame) { FactoryBot.create(:user_video_frame, user: user, video_frame: video_frame, active: true) }
      let!(:inactive_frame) { FactoryBot.create(:user_video_frame, user: user, video_frame: video_frame, active: false) }

      it 'returns only active user video frames' do
        expect(UserVideoFrame.active).to include(active_frame)
        expect(UserVideoFrame.active).not_to include(inactive_frame)
      end
    end
  end



  describe '#needs_identity_image_regeneration?' do
    context 'when identity_photo_url is blank' do
      before { user_video_frame.update_column(:identity_photo_url, nil) }

      it 'returns true' do
        expect(user_video_frame.needs_identity_image_regeneration?).to be true
      end
    end

    context 'when identity_photo_url is empty string' do
      before { user_video_frame.update_column(:identity_photo_url, '') }

      it 'returns true' do
        expect(user_video_frame.needs_identity_image_regeneration?).to be true
      end
    end

    context 'when identity_photo_url contains fallback placeholder' do
      before { user_video_frame.update_column(:identity_photo_url, 'https://example.com/identity.png') }

      it 'returns true' do
        expect(user_video_frame.needs_identity_image_regeneration?).to be true
      end
    end

    context 'when identity_photo_url contains placeholder' do
      before { user_video_frame.update_column(:identity_photo_url, 'https://example.com/placeholder.jpg') }

      it 'returns true' do
        expect(user_video_frame.needs_identity_image_regeneration?).to be true
      end
    end

    context 'when identity_photo_url is a valid generated URL' do
      before { user_video_frame.update_column(:identity_photo_url, 'https://cdn.example.com/generated-identity-123.png') }

      it 'returns false' do
        expect(user_video_frame.needs_identity_image_regeneration?).to be false
      end
    end
  end

  describe 'factory' do
    it 'creates a valid user video frame' do
      expect(user_video_frame).to be_valid
      expect(user_video_frame.user).to eq(user)
      expect(user_video_frame.video_frame).to eq(video_frame)
      expect(user_video_frame.identity_photo_url).to be_present
      expect(user_video_frame.active).to be true
    end

    it 'handles unique constraint properly' do
      # First creation should work
      first_frame = FactoryBot.create(:user_video_frame, user: user, video_frame: video_frame)
      expect(first_frame).to be_persisted

      # Second creation with same user and video_frame should return existing record
      second_frame = FactoryBot.create(:user_video_frame, user: user, video_frame: video_frame)
      expect(second_frame.id).to eq(first_frame.id)
    end
  end
end
