FactoryBot.define do
  factory :video_frame do
    video_type { 'portrait' }
    font
    active { true }

    # Handle unique constraint on video_type and font_id
    initialize_with do
      existing = VideoFrame.find_by(video_type: video_type, font_id: font.id)
      existing || VideoFrame.new(attributes)
    end

    trait :landscape do
      video_type { 'landscape' }
    end

    trait :square do
      video_type { 'square' }
    end
  end
end
