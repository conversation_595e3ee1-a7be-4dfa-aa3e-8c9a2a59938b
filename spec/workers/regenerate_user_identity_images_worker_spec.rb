require 'rails_helper'

RSpec.describe RegenerateUserIdentityImagesWorker, type: :worker do
  let(:user) { FactoryBot.create(:user, name: "Test User") }
  let(:font) { FactoryBot.create(:font, name_font: "Noto Sans Telugu", badge_font: "Noto Sans Telugu") }
  let(:video_frame1) { FactoryBot.create(:video_frame, font: font, active: true) }
  let(:video_frame2) { FactoryBot.create(:video_frame, :landscape, font: font, active: true) }
  let!(:user_video_frame1) { FactoryBot.create(:user_video_frame, user: user, video_frame: video_frame1, identity_photo_url: 'https://example.com/old_portrait.png', active: true) }
  let!(:user_video_frame2) { FactoryBot.create(:user_video_frame, user: user, video_frame: video_frame2, identity_photo_url: 'https://example.com/old_landscape.png', active: true) }

  subject { described_class.new }

  before do
    # Mock the capture_html_as_image method to avoid external dependencies
    allow(subject).to receive(:capture_html_as_image).and_return({
      'cdn_url' => 'https://example.com/new_identity_image.png'
    })
  end

  describe '#perform' do
    context 'with valid user' do
      it 'should regenerate identity images for all active video frames' do
        expect {
          subject.perform(user.id)
        }.to change { UserVideoFrame.where(user: user, active: true).count }.by(0)
        
        # Should have 2 new active frames and 2 old inactive frames
        expect(UserVideoFrame.where(user: user).count).to eq(4)
        expect(UserVideoFrame.where(user: user, active: true).count).to eq(2)
        expect(UserVideoFrame.where(user: user, active: false).count).to eq(2)
      end

      it 'should create new frames before marking old ones inactive' do
        # Track the sequence of operations
        operations = []
        
        allow(UserVideoFrame).to receive(:create!) do |attrs|
          operations << "create_new_frame_#{attrs[:video_frame].video_type}"
          FactoryBot.create(:user_video_frame, attrs)
        end
        
        allow_any_instance_of(UserVideoFrame).to receive(:update!) do |frame, attrs|
          operations << "deactivate_old_frame_#{frame.video_frame_id}"
          true
        end
        
        subject.perform(user.id)
        
        # Verify new frames are created before old ones are deactivated
        create_operations = operations.select { |op| op.start_with?('create_new_frame') }
        deactivate_operations = operations.select { |op| op.start_with?('deactivate_old_frame') }
        
        expect(create_operations.length).to eq(2)
        expect(deactivate_operations.length).to eq(2)
      end

      it 'should set correct identity_photo_url for new frames' do
        subject.perform(user.id)
        
        new_frames = UserVideoFrame.where(user: user, active: true)
        expect(new_frames.count).to eq(2)
        
        new_frames.each do |frame|
          expect(frame.identity_photo_url).to eq('https://example.com/new_identity_image.png')
        end
      end

      it 'should preserve video frame associations' do
        subject.perform(user.id)
        
        new_frames = UserVideoFrame.where(user: user, active: true)
        video_frame_ids = new_frames.pluck(:video_frame_id).sort
        expected_ids = [video_frame1.id, video_frame2.id].sort
        
        expect(video_frame_ids).to eq(expected_ids)
      end

      it 'should log successful operations' do
        expect(Rails.logger).to receive(:info).with("New identity image generated for user #{user.id}, video_frame #{video_frame1.id}")
        expect(Rails.logger).to receive(:info).with("New identity image generated for user #{user.id}, video_frame #{video_frame2.id}")
        expect(Rails.logger).to receive(:info).with("Marked old identity image as inactive for user #{user.id}, video_frame #{video_frame1.id}")
        expect(Rails.logger).to receive(:info).with("Marked old identity image as inactive for user #{user.id}, video_frame #{video_frame2.id}")
        expect(Rails.logger).to receive(:info).with("Identity images regeneration completed for user #{user.id}. Created 2 new frames, deactivated 2 old frames")
        
        subject.perform(user.id)
      end
    end

    context 'with invalid user' do
      it 'should return early for non-existent user' do
        expect(subject).not_to receive(:capture_html_as_image)
        expect(Rails.logger).not_to receive(:info)
        
        subject.perform(99999)
      end
    end

    context 'with no active video frames' do
      before do
        VideoFrame.update_all(active: false)
      end

      it 'should return early when no active video frames exist' do
        expect(subject).not_to receive(:capture_html_as_image)
        expect(Rails.logger).not_to receive(:info)
        
        subject.perform(user.id)
      end
    end

    context 'when image generation fails' do
      before do
        allow(subject).to receive(:capture_html_as_image).and_raise("Image generation failed")
      end

      it 'should keep old frames active when all generations fail' do
        expect {
          subject.perform(user.id)
        }.to raise_error("Image generation failed")
        
        # Old frames should remain active
        expect(user_video_frame1.reload.active).to be true
        expect(user_video_frame2.reload.active).to be true
        
        # No new frames should be created
        expect(UserVideoFrame.where(user: user, active: true).count).to eq(2)
      end

      it 'should log errors for failed generations' do
        expect(Rails.logger).to receive(:error).with(/Failed to generate identity image for user #{user.id}, video_frame #{video_frame1.id}/)
        expect(Rails.logger).to receive(:error).with(/Failed to generate identity image for user #{user.id}, video_frame #{video_frame2.id}/)
        expect(Rails.logger).to receive(:error).with("No new identity images were created for user #{user.id}. Keeping existing frames active.")
        
        expect {
          subject.perform(user.id)
        }.to raise_error("Image generation failed")
      end

      it 'should notify Honeybadger of failures' do
        expect(Honeybadger).to receive(:notify).at_least(:once)
        
        expect {
          subject.perform(user.id)
        }.to raise_error("Image generation failed")
      end
    end

    context 'when partial generation fails' do
      before do
        call_count = 0
        allow(subject).to receive(:capture_html_as_image) do
          call_count += 1
          if call_count == 1
            { 'cdn_url' => 'https://example.com/new_portrait.png' }
          else
            raise "Second generation failed"
          end
        end
      end

      it 'should handle partial failures gracefully' do
        expect {
          subject.perform(user.id)
        }.not_to raise_error
        
        # Should have at least one successful generation
        active_frames = UserVideoFrame.where(user: user, active: true)
        expect(active_frames.count).to be >= 1
        
        # Should have one successful new frame
        successful_frame = active_frames.find { |f| f.identity_photo_url == 'https://example.com/new_portrait.png' }
        expect(successful_frame).to be_present
      end

      it 'should log partial success' do
        expect(Rails.logger).to receive(:info).with("New identity image generated for user #{user.id}, video_frame #{video_frame1.id}")
        expect(Rails.logger).to receive(:error).with(/Failed to generate identity image for user #{user.id}, video_frame #{video_frame2.id}/)
        
        subject.perform(user.id)
      end
    end

    context 'when user has no existing frames' do
      let(:user_without_frames) { FactoryBot.create(:user, name: "User Without Frames") }

      it 'should create new frames without trying to deactivate old ones' do
        expect {
          subject.perform(user_without_frames.id)
        }.to change { UserVideoFrame.where(user: user_without_frames).count }.from(0).to(2)
        
        new_frames = UserVideoFrame.where(user: user_without_frames, active: true)
        expect(new_frames.count).to eq(2)
      end

      it 'should log creation without deactivation' do
        expect(Rails.logger).to receive(:info).with("New identity image generated for user #{user_without_frames.id}, video_frame #{video_frame1.id}")
        expect(Rails.logger).to receive(:info).with("New identity image generated for user #{user_without_frames.id}, video_frame #{video_frame2.id}")
        expect(Rails.logger).to receive(:info).with("Identity images regeneration completed for user #{user_without_frames.id}. Created 2 new frames, deactivated 0 old frames")
        
        subject.perform(user_without_frames.id)
      end
    end
  end

  describe '#generate_identity_image' do
    it 'should call capture_html_as_image with correct parameters' do
      expect(subject).to receive(:capture_html_as_image).with(
        an_instance_of(String), 
        '#top-outer-container'
      ).and_return({ 'cdn_url' => 'https://example.com/test.png' })
      
      result = subject.send(:generate_identity_image, user, video_frame1)
      expect(result).to eq('https://example.com/test.png')
    end

    it 'should raise error when capture returns no URL' do
      allow(subject).to receive(:capture_html_as_image).and_return({})
      
      expect {
        subject.send(:generate_identity_image, user, video_frame1)
      }.to raise_error(/Did not receive url from captured html/)
    end
  end

  describe 'Sidekiq configuration' do
    it 'should have correct queue configuration' do
      expect(described_class.sidekiq_options['queue']).to eq(:video_posters_generation)
    end

    it 'should have retry configuration' do
      expect(described_class.sidekiq_options['retry']).to eq(3)
    end

    it 'should have lock configuration' do
      expect(described_class.sidekiq_options['lock']).to eq(:until_and_while_executing)
    end
  end
end
